# 电商网站改造为物流公司官网计划

## 项目概述
将现有的电商模板网站改造为物流公司官方网站，保持原有风格和布局，移除电商功能，添加物流服务相关内容。

## 改造原则
- **保持原有风格和布局**：不改变网站的视觉设计和页面结构
- **仅做内容变更**：重点在于内容替换，而非重新设计
- **移除电商功能**：清理所有购买相关的功能模块
- **服务化展示**：将商品展示改为服务类目展示

## 需要移除的功能模块

### 1. 购物车相关
- **页面**: `/cart`
- **组件**: `Cart/`, `CartSidebarModal/`
- **状态管理**: `cart-slice.ts`
- **图标**: 购物车图标

### 2. 登录注册系统
- **页面**: `/signin`, `/signup`, `/my-account`
- **组件**: `Auth/Signin/`, `Auth/Signup/`, `MyAccount/`

### 3. 心愿单功能
- **页面**: `/wishlist`
- **组件**: `Wishlist/`
- **状态管理**: `wishlist-slice.ts`

### 4. 结账流程
- **页面**: `/checkout`
- **组件**: `Checkout/`

### 5. 搜索功能
- **组件**: `SearchForm.tsx`
- **Header中的搜索框**

### 6. APP下载相关
- **Footer中的APP下载链接**
- **相关下载图标**

## 需要改造的功能模块

### 1. 商品展示 → 服务展示
- **组件改造**:
  - `Shop/` → `Services/`
  - `ShopDetails/` → `ServiceDetails/`
  - `ShopWithSidebar/` → `ServicesWithSidebar/`
  - `ShopWithoutSidebar/` → `ServicesWithoutSidebar/`

- **内容变更**:
  - 移除价格显示
  - 商品名称 → 服务名称
  - 商品描述 → 服务描述
  - 商品图片 → 服务图片

### 2. 分类系统
- **组件**: `Categories/`
- **内容**: 商品分类 → 物流服务分类
  - 国际快递
  - 国内快递
  - 仓储服务
  - 供应链管理
  - 特殊物品运输
  - 冷链运输

### 3. 首页模块调整
- **Hero区域**: 更换为物流公司宣传内容
- **最新产品** → **主要服务**
- **畅销产品** → **热门服务**
- **促销横幅** → **服务优势宣传**

### 4. 导航菜单调整
- **主导航**:
  - "Popular" → "首页"
  - "Shop" → "服务"
  - "Contact" → "联系我们"
  
- **删除子菜单项**:
  - Shop With Sidebar
  - Shop Without Sidebar
  - Checkout
  - Cart
  - Wishlist
  - Sign in/up
  - My Account

- **保留菜单项**:
  - Contact
  - Error
  - Mail Success
  - Blog相关页面

## 内容更新计划

### 1. 公司信息
- **公司名称**: 待定物流公司名称
- **业务范围**: 快递、仓储、供应链管理
- **服务优势**: 时效性、安全性、覆盖范围

### 2. 服务类目
- **国际快递**: 全球快递服务
- **国内快递**: 国内当日达、次日达
- **仓储服务**: 智能仓储管理
- **供应链管理**: 端到端供应链解决方案
- **特殊服务**: 冷链、危险品、大件运输

### 3. 图片资源更新
- **Hero图片**: 物流运输相关
- **服务图片**: 快递、仓储、运输车辆
- **图标**: 运输、时间、全球等物流相关图标

## 技术实施步骤

### 阶段一：清理电商功能
1. 移除购物车相关页面和组件
2. 移除登录注册相关页面和组件
3. 移除心愿单功能
4. 移除结账流程
5. 移除搜索功能
6. 清理Redux中相关状态管理

### 阶段二：调整导航和路由
1. 更新菜单数据结构
2. 删除不需要的路由页面
3. 更新Header组件，移除购物车、搜索等图标

### 阶段三：改造产品模块为服务模块
1. 重命名组件文件夹和文件
2. 更新组件内部逻辑，移除价格相关
3. 调整数据结构，适配服务展示
4. 更新类型定义

### 阶段四：内容和样式调整
1. 更新首页内容
2. 更新服务分类数据
3. 更换图片资源
4. 调整文案内容

### 阶段五：测试和优化
1. 功能测试
2. 样式检查
3. 响应式测试
4. 性能优化

## 文件修改清单

### 需要删除的文件/文件夹
- `src/app/(site)/(pages)/cart/`
- `src/app/(site)/(pages)/checkout/`
- `src/app/(site)/(pages)/wishlist/`
- `src/app/(site)/(pages)/signin/`
- `src/app/(site)/(pages)/signup/`
- `src/app/(site)/(pages)/my-account/`
- `src/components/Auth/`
- `src/components/Cart/`
- `src/components/Checkout/`
- `src/components/Wishlist/`
- `src/components/MyAccount/`
- `src/components/Common/CartSidebarModal/`
- `src/redux/features/cart-slice.ts`
- `src/redux/features/wishlist-slice.ts`

### 需要重命名的文件/文件夹
- `src/app/(site)/(pages)/shop-details/` → `service-details/`
- `src/app/(site)/(pages)/shop-with-sidebar/` → `services-with-sidebar/`
- `src/app/(site)/(pages)/shop-without-sidebar/` → `services-without-sidebar/`
- `src/components/Shop/` → `Services/`
- `src/components/ShopDetails/` → `ServiceDetails/`
- `src/components/ShopWithSidebar/` → `ServicesWithSidebar/`
- `src/components/ShopWithoutSidebar/` → `ServicesWithoutSidebar/`

### 需要修改的主要文件
- `src/components/Header/menuData.ts` - 更新导航菜单
- `src/components/Header/index.tsx` - 移除购物车、搜索等功能
- `src/components/Footer/index.tsx` - 更新公司信息，移除APP下载
- `src/components/Home/Categories/categoryData.ts` - 更新为服务分类
- `src/components/Home/index.tsx` - 调整首页布局
- `src/types/product.ts` → `service.ts` - 更新类型定义

## 注意事项
1. **不要自行启停服务或安装npm包** - 如需要会通知手工操作
2. **保持原有样式** - 只修改内容，不改变CSS样式
3. **渐进式改造** - 分步骤进行，确保每步都能正常运行
4. **备份重要文件** - 改造前备份关键配置文件

## 预估工作量
- **清理电商功能**: 2-3小时
- **组件重命名和调整**: 3-4小时  
- **内容更新**: 2-3小时
- **测试和优化**: 1-2小时
- **总计**: 8-12小时

## 完成标准
1. 所有电商功能已移除
2. 网站可正常访问，无报错
3. 服务展示功能正常
4. 样式保持原有风格
5. 响应式布局正常
6. 内容已更新为物流相关