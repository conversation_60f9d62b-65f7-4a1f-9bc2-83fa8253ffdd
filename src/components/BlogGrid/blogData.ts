import { BlogItem } from "@/types/blogItem";

const blogData: BlogItem[] = [
  {
    date: "Mar 27, 2024",
    views: 300000,
    title: "The Future of Global Logistics: Trends and Innovations",
    img: "/images/blog/blog-01.jpg",
  },
  {
    date: "Mar 25, 2024",
    views: 250000,
    title: "How to Optimize Your Supply Chain for Maximum Efficiency",
    img: "/images/blog/blog-02.jpg",
  },
  {
    date: "Mar 23, 2024",
    views: 180000,
    title: "Cold Chain Logistics: Best Practices for Temperature-Sensitive Goods",
    img: "/images/blog/blog-03.jpg",
  },
  {
    date: "Mar 20, 2024",
    views: 50000,
    title: "International Shipping Regulations: A Complete Guide",
    img: "/images/blog/blog-04.jpg",
  },
  {
    date: "Mar 18, 2024",
    views: 120000,
    title: "Sustainable Logistics: Reducing Environmental Impact",
    img: "/images/blog/blog-05.jpg",
  },
  {
    date: "Mar 15, 2024",
    views: 75000,
    title: "Technology in Logistics: AI and Automation Trends",
    img: "/images/blog/blog-06.jpg",
  },
  {
    date: "Mar 12, 2024",
    views: 90000,
    title: "Last-Mile Delivery Solutions for Urban Areas",
    img: "/images/blog/blog-07.jpg",
  },
  {
    date: "Mar 10, 2024",
    views: 150000,
    title: "Warehouse Management: Strategies for Improved Operations",
    img: "/images/blog/blog-08.jpg",
  },
  {
    date: "Mar 8, 2024",
    views: 60000,
    title: "Cross-Border E-commerce Logistics: Challenges and Solutions",
    img: "/images/blog/blog-09.jpg",
  },
];

export default blogData;
