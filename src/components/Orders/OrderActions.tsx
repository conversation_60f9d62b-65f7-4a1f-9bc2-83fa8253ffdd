import React from "react";

const OrderActions = ({ toggleEdit, toggleDetails }: any) => {
  return (
    <>
      <button
        onClick={toggleDetails}
        className="hover:bg-gray-2 rounded-sm p-2"
      >
        <svg
          width="18"
          height="16"
          viewBox="0 0 18 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8.99935 4.87504C7.27346 4.87504 5.87435 6.27415 5.87435 8.00004C5.87435 9.72593 7.27346 11.125 8.99935 11.125C10.7252 11.125 12.1243 9.72593 12.1243 8.00004C12.1243 6.27415 10.7252 4.87504 8.99935 4.87504ZM7.12435 8.00004C7.12435 6.96451 7.96382 6.12504 8.99935 6.12504C10.0349 6.12504 10.8743 6.96451 10.8743 8.00004C10.8743 9.03557 10.0349 9.87504 8.99935 9.87504C7.96382 9.87504 7.12435 9.03557 7.12435 8.00004Z"
            fill="#495270"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M8.99935 0.708374C5.23757 0.708374 2.70376 2.96187 1.23315 4.87243L1.20663 4.90687C0.874048 5.33882 0.567732 5.73664 0.359922 6.20704C0.137386 6.71077 0.0410156 7.25978 0.0410156 8.00004C0.0410156 8.7403 0.137386 9.28932 0.359922 9.79304C0.567734 10.2634 0.87405 10.6613 1.20664 11.0932L1.23316 11.1277C2.70376 13.0382 5.23757 15.2917 8.99935 15.2917C12.7611 15.2917 15.2949 13.0382 16.7655 11.1277L16.792 11.0932C17.1246 10.6613 17.431 10.2634 17.6388 9.79304C17.8613 9.28932 17.9577 8.7403 17.9577 8.00004C17.9577 7.25978 17.8613 6.71077 17.6388 6.20704C17.431 5.73663 17.1246 5.33881 16.792 4.90685L16.7655 4.87243C15.2949 2.96187 12.7611 0.708374 8.99935 0.708374ZM2.2237 5.63487C3.58155 3.87081 5.79132 1.95837 8.99935 1.95837C12.2074 1.95837 14.4172 3.87081 15.775 5.63487C16.1405 6.1097 16.3546 6.39342 16.4954 6.71216C16.627 7.01005 16.7077 7.37415 16.7077 8.00004C16.7077 8.62593 16.627 8.99003 16.4954 9.28792C16.3546 9.60666 16.1405 9.89038 15.775 10.3652C14.4172 12.1293 12.2074 14.0417 8.99935 14.0417C5.79132 14.0417 3.58155 12.1293 2.2237 10.3652C1.85821 9.89038 1.64413 9.60666 1.50332 9.28792C1.37171 8.99003 1.29102 8.62593 1.29102 8.00004C1.29102 7.37415 1.37171 7.01005 1.50332 6.71216C1.64413 6.39342 1.85821 6.1097 2.2237 5.63487Z"
            fill="#495270"
          />
        </svg>
      </button>
      <button onClick={toggleEdit} className="hover:bg-gray-2 rounded-sm p-2">
        <svg
          width="16"
          height="18"
          viewBox="0 0 16 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M9.75002 0.992536C11.0179 -0.275344 13.0735 -0.275344 14.3414 0.992536C15.6093 2.26042 15.6093 4.31606 14.3414 5.58394L8.01308 11.9123C7.65642 12.269 7.4386 12.4868 7.19583 12.6762C6.90981 12.8993 6.60033 13.0905 6.27287 13.2466C5.99494 13.3791 5.70269 13.4765 5.22413 13.6359L2.99692 14.3783L2.4622 14.5566C1.98358 14.7161 1.45589 14.5916 1.09915 14.2348C0.7424 13.8781 0.617832 13.3504 0.777373 12.8718L1.69801 10.1098C1.8575 9.63128 1.9549 9.33902 2.08736 9.06109C2.24342 8.73363 2.43469 8.42415 2.65778 8.13813C2.84714 7.89536 3.06498 7.67754 3.42169 7.32087L9.75002 0.992536ZM2.96809 13.0703L2.26362 12.3659L2.87064 10.5448C3.04737 10.0146 3.12037 9.79903 3.21577 9.59886C3.33276 9.35336 3.47616 9.12134 3.64342 8.9069C3.77979 8.73206 3.93997 8.57036 4.33516 8.17517L9.24431 3.26602C9.44694 3.77435 9.78979 4.38786 10.3679 4.96602C10.9461 5.54417 11.5596 5.88702 12.0679 6.08966L7.15879 10.9988C6.7636 11.394 6.6019 11.5542 6.42706 11.6905C6.21262 11.8578 5.9806 12.0012 5.7351 12.1182C5.53493 12.2136 5.31935 12.2866 4.78915 12.4633L2.96809 13.0703ZM13.064 5.09363C12.9608 5.07098 12.8318 5.03689 12.6851 4.986C12.2817 4.84606 11.7511 4.58145 11.2518 4.08213C10.7525 3.58282 10.4879 3.05226 10.348 2.6489C10.2971 2.50221 10.263 2.37319 10.2403 2.27L10.6339 1.87642C11.4136 1.09669 12.6778 1.09669 13.4575 1.87642C14.2373 2.65615 14.2373 3.92033 13.4575 4.70005L13.064 5.09363ZM0.709001 17.3333C0.709001 16.9881 0.988823 16.7083 1.334 16.7083H14.6673V17.9583H1.334C0.988823 17.9583 0.709001 17.6785 0.709001 17.3333Z"
            fill="#495270"
          />
        </svg>
      </button>
    </>
  );
};

export default OrderActions;
