import { Menu } from "@/types/Menu";

export const menuData: Menu[] = [
  {
    id: 1,
    title: "Home",
    newTab: false,
    path: "/",
  },
  {
    id: 2,
    title: "Services",
    newTab: false,
    path: "/services-with-sidebar",
  },
  {
    id: 3,
    title: "About Us",
    newTab: false,
    path: "/about",
  },
  {
    id: 4,
    title: "Contact Us",
    newTab: false,
    path: "/contact",
  },
  {
    id: 6,
    title: "Pages",
    newTab: false,
    path: "/",
    submenu: [
      {
        id: 61,
        title: "Services Showcase",
        newTab: false,
        path: "/services-with-sidebar",
      },
      {
        id: 62,
        title: "Services List",
        newTab: false,
        path: "/services-without-sidebar",
      },
      {
        id: 70,
        title: "Contact Us",
        newTab: false,
        path: "/contact",
      },
      {
        id: 62,
        title: "Error Page",
        newTab: false,
        path: "/error",
      },
      {
        id: 63,
        title: "Mail Success",
        newTab: false,
        path: "/mail-success",
      },
    ],
  },
  {
    id: 7,
    title: "News",
    newTab: false,
    path: "/",
    submenu: [
      {
        id: 71,
        title: "News Grid (With Sidebar)",
        newTab: false,
        path: "/blogs/blog-grid-with-sidebar",
      },
      {
        id: 72,
        title: "News Grid",
        newTab: false,
        path: "/blogs/blog-grid",
      },
      {
        id: 73,
        title: "News Details (With Sidebar)",
        newTab: false,
        path: "/blogs/blog-details-with-sidebar",
      },
      {
        id: 74,
        title: "News Details",
        newTab: false,
        path: "/blogs/blog-details",
      },
    ],
  },
];
