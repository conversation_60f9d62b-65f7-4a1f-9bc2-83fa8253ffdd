"use client";
import React, { useState } from "react";

const SearchForm = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle search functionality here
    console.log("Searching for:", searchTerm);
  };

  return (
    <div className="bg-white shadow-1 rounded-lg p-6 mb-6">
      <h3 className="font-semibold text-lg text-dark mb-4">搜索</h3>
      <form onSubmit={handleSubmit}>
        <div className="relative">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="搜索文章..."
            className="w-full px-4 py-3 border border-gray-3 rounded-md focus:outline-none focus:border-blue"
          />
          <button
            type="submit"
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
          >
            <svg
              className="fill-current text-dark hover:text-blue w-5 h-5"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.16667 3.33333C5.94501 3.33333 3.33334 5.94501 3.33334 9.16667C3.33334 12.3883 5.94501 15 9.16667 15C12.3883 15 15 12.3883 15 9.16667C15 5.94501 12.3883 3.33333 9.16667 3.33333ZM1.66667 9.16667C1.66667 5.02453 5.02453 1.66667 9.16667 1.66667C13.3088 1.66667 16.6667 5.02453 16.6667 9.16667C16.6667 13.3088 13.3088 16.6667 9.16667 16.6667C5.02453 16.6667 1.66667 13.3088 1.66667 9.16667Z"
                fill=""
              />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M13.2857 13.2857C13.611 12.9603 14.1385 12.9603 14.4638 13.2857L18.0892 16.9111C18.4146 17.2364 18.4146 17.7639 18.0892 18.0892C17.7639 18.4146 17.2364 18.4146 16.9111 18.0892L13.2857 14.4638C12.9603 14.1385 12.9603 13.611 13.2857 13.2857Z"
                fill=""
              />
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default SearchForm;