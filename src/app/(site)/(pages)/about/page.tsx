import React from "react";
import Breadcrumb from "@/components/Common/Breadcrumb";

const AboutPage = () => {
  return (
    <>
      <Breadcrumb
        title={"About Us"}
        pages={["Home", "About Us"]}
      />

      <section className="overflow-hidden pt-15 pb-20">
        <div className="max-w-[1170px] w-full mx-auto px-4 sm:px-8 xl:px-0">
          <div className="text-center mb-15">
            <h2 className="font-semibold text-2xl xl:text-heading-3 text-dark mb-6">
              Professional Logistics Service Provider
            </h2>
            <p className="text-lg text-dark-4 max-w-[600px] mx-auto">
              We are committed to providing safe, fast, and reliable logistics solutions, covering domestic and international transportation services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">Fast Delivery</h3>
              <p className="text-dark-4">
                Providing 24-hour fast delivery service to ensure goods reach their destination on time.
              </p>
            </div>

            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">Safety Guarantee</h3>
              <p className="text-dark-4">
                Strict packaging and transportation standards to ensure the safety of goods during transportation.
              </p>
            </div>

            <div className="bg-white shadow-1 rounded-lg p-6">
              <div className="w-12 h-12 bg-blue rounded-lg flex items-center justify-center mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064" />
                </svg>
              </div>
              <h3 className="font-semibold text-lg text-dark mb-3">Global Network</h3>
              <p className="text-dark-4">
                Logistics network covering major cities worldwide, providing international transportation services.
              </p>
            </div>
          </div>

          <div className="mt-15 bg-gray-1 rounded-lg p-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="font-semibold text-xl text-dark mb-4">
                  Our Commitment
                </h3>
                <p className="text-dark-4 mb-4">
                  As a professional logistics service provider, we always focus on customer needs
                  and provide customized logistics solutions.
                </p>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">7×24 Hour Customer Service</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">Real-time Cargo Tracking</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue rounded-full"></div>
                    <span className="text-dark-4">Professional Team Support</span>
                  </li>
                </ul>
              </div>

              <div className="text-center">
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <div className="text-3xl font-bold text-blue">1000+</div>
                    <p className="text-dark-4">Customers Served</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">50+</div>
                    <p className="text-dark-4">Cities Served</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">99.8%</div>
                    <p className="text-dark-4">On-time Rate</p>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-blue">24/7</div>
                    <p className="text-dark-4">Customer Service</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default AboutPage;
