@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply font-euclid-circular-a font-normal text-base text-dark-3 relative z-1;
  }
}

@layer components {
  .dropdown {
    @apply flex-col gap-0 min-w-max xl:w-[193px] mt-2 lg:mt-0 bg-white shadow-2 ease-in duration-200 py-2.5 rounded-md border border-gray-3 left-0 hidden
    xl:translate-y-10 xl:opacity-0 xl:invisible xl:absolute xl:flex
    xl:group-hover:opacity-100 xl:group-hover:visible;
  }
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

/* third-party libraries CSS */

/* clears the ‘X’ from Internet Explorer */
input[type='search']::-ms-clear {
  display: none;
  width: 0;
  height: 0;
}
input[type='search']::-ms-reveal {
  display: none;
  width: 0;
  height: 0;
}
/* clears the ‘X’ from Chrome */
input[type='search']::-webkit-search-decoration,
input[type='search']::-webkit-search-cancel-button,
input[type='search']::-webkit-search-results-button,
input[type='search']::-webkit-search-results-decoration {
  display: none;
}
.custom-search {
  -webkit-border-radius: 0;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
}

/* The container must be positioned relative: */
.custom-select select {
  display: none; /*hide original SELECT element: */
}

.select-selected {
  @apply bg-gray-1 rounded-l-[5px] border border-gray-3 !border-r-0 cursor-pointer text-dark text-custom-sm py-[11px] pl-3.5 pr-8 relative;
}

/* Style the arrow inside the select element: */
.select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='10' height='6' viewBox='0 0 10 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M5.00005 5.54998C4.88755 5.54998 4.7938 5.51248 4.70005 5.43748L0.387549 1.19998C0.218799 1.03123 0.218799 0.768726 0.387549 0.599976C0.556299 0.431226 0.818799 0.431226 0.987549 0.599976L5.00005 4.51873L9.01255 0.562476C9.1813 0.393726 9.4438 0.393726 9.61255 0.562476C9.7813 0.731226 9.7813 0.993726 9.61255 1.16248L5.30005 5.39998C5.2063 5.49373 5.11255 5.54998 5.00005 5.54998Z' fill='%231C274C'/%3E%3C/svg%3E%0A");
  @apply absolute top-1/2 -translate-y-1/2 right-4.5 ease-out duration-200;
}

/* Point the arrow upwards when the select box is open (active): */
.select-selected.select-arrow-active:after {
  @apply rotate-180 mt-0.5;
}

/* style the items (options), including the selected item: */
.select-items div {
  @apply cursor-pointer text-dark-3 text-custom-sm py-[7px] px-4.5;
}

/* Style items (options): */
.select-items {
  @apply absolute top-full left-0 right-0 z-99 shadow-2 border border-gray-3 bg-white rounded-md py-2.5 mt-1.5;
}

/* Hide the items when the select box is closed: */
.select-hide {
  display: none;
}

.select-items div:hover,
.same-as-selected {
  @apply bg-gray-1 text-dark;
}

.custom-select-2 .select-selected {
  @apply bg-white rounded-md !border-r text-dark-4 py-1.5 pl-3 pr-9;
}
.custom-select-2 .select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='14' height='8' viewBox='0 0 14 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M1.41444 1.03563L1.41443 1.03565L1.41725 1.0384L6.76725 6.2634L7.00126 6.49194L7.23418 6.26229L12.5842 0.987287L12.5842 0.987293L12.5858 0.985629C12.6807 0.890804 12.8196 0.890804 12.9144 0.985629C13.009 1.08018 13.0093 1.21861 12.9153 1.31341C12.915 1.31368 12.9147 1.31395 12.9144 1.31422L7.16652 6.96217L7.16651 6.96216L7.16444 6.96422C7.06814 7.06053 7.02327 7.06659 7.00015 7.06659C6.94122 7.06659 6.89018 7.05216 6.8204 6.99902L1.08502 1.36339C0.991024 1.26859 0.991301 1.13018 1.08585 1.03563C1.18067 0.940804 1.31962 0.940804 1.41444 1.03563Z' fill='%238D93A5' stroke='%238D93A5' stroke-width='0.666667'/%3E%3C/svg%3E%0A");
  @apply right-3.5;
}

.custom-select-common .select-selected {
  @apply bg-gray-1 rounded-md !border-r text-dark-4 py-3 pl-5 pr-9 duration-200 focus:border-transparent focus:shadow-input focus:ring-2 focus:ring-blue/20;
}
.custom-select-common .select-selected:after {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2.41469 5.03569L2.41467 5.03571L2.41749 5.03846L7.76749 10.2635L8.0015 10.492L8.23442 10.2623L13.5844 4.98735L13.5844 4.98735L13.5861 4.98569C13.6809 4.89086 13.8199 4.89087 13.9147 4.98569C14.0092 5.08024 14.0095 5.21864 13.9155 5.31345C13.9152 5.31373 13.915 5.31401 13.9147 5.31429L8.16676 10.9622L8.16676 10.9622L8.16469 10.9643C8.06838 11.0606 8.02352 11.0667 8.00039 11.0667C7.94147 11.0667 7.89042 11.0522 7.82064 10.9991L2.08526 5.36345C1.99127 5.26865 1.99154 5.13024 2.08609 5.03569C2.18092 4.94086 2.31986 4.94086 2.41469 5.03569Z' fill='%238D93A5' stroke='%238D93A5' stroke-width='0.666667'/%3E%3C/svg%3E%0A");
  @apply right-4 block mt-1;
}
.custom-select-common .select-selected.select-arrow-active:after {
  @apply -mt-0.5;
}


.hero-carousel .swiper-pagination-bullet {
  @apply h-1 w-4 rounded-[11px] bg-[#DDD];
}

.hero-carousel .swiper-pagination-bullet-active {
  @apply w-5.5 bg-blue;
}

.hero-carousel .swiper-pagination {
  @apply xl:!bottom-5;
}

.common-carousel .swiper-button-next:after,
.common-carousel .swiper-button-prev:after {
  @apply hidden;
}

.common-carousel .swiper-button-next,
.common-carousel .swiper-button-prev {
  @apply !static h-9 w-9 rounded-lg bg-white text-dark border border-gray-3 m-0 ease-out duration-200 hover:bg-blue hover:text-white hover:border-blue;
}

.common-carousel .swiper-button-next svg,
.common-carousel .swiper-button-prev svg {
  @apply w-auto h-auto;
}

.img-zoom-container img {
  transition: 0.8s;
}
.img-zoom-container img:hover {
  transform: scale(2) rotate(0deg);
  transition: 0.8s;
}

.priceSlide .noUi-target {
  @apply mb-5 mt-9 border-none bg-transparent shadow-none;
}
.priceSlide .noUi-connects {
  @apply h-1 rounded-full bg-gray-3;
}
.priceSlide .noUi-connect {
  @apply h-1 rounded-full bg-blue;
}
.priceSlide .noUi-horizontal .noUi-handle {
  @apply -top-3 h-7 w-7 rounded-full border border-gray-4 bg-white shadow-range before:block before:absolute before:left-1/2 before:top-1/2 before:-translate-x-1/2 before:-translate-y-1/2 before:w-4 before:h-4 before:rounded-full before:bg-blue;
}
.priceSlide .noUi-tooltip {
  @apply border-none p-0 bg-transparent text-custom-sm text-dark-4;
}
.noUi-horizontal .noUi-handle:after,
.noUi-horizontal .noUi-handle:before {
  @apply hidden;
}


.range-slider {
 @apply w-full !h-[4px] rounded-md bg-blue relative mb-3;
}

.slider .thumb {
  @apply w-6 h-6 bg-blue-dark rounded-full absolute -top-2 border-[5px] border-blue-light-4;
}

.range-slider__thumb {
  @apply !bg-white border-[1px] border-blue-light-4 flex justify-center items-center !w-7 !h-7;
}

.range-slider__thumb::after {
  content: "";
  width: 16px;
  height: 16px;
  display: block;
  border-radius: 100%;
  position: absolute;
  @apply bg-blue;
}

.range-slider__range {
  @apply !bg-blue;
}